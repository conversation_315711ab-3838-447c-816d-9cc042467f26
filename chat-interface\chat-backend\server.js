require('dotenv').config();
const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');

const app = express();
const SESSION_FILE = path.join(__dirname, 'sessions.json');

// Load or initialize sessions
function loadSessions() {
    try {
        return fs.existsSync(SESSION_FILE)
            ? JSON.parse(fs.readFileSync(SESSION_FILE))
            : {};
    } catch (e) {
        console.error('Error loading sessions:', e);
        return {};
    }
}

function saveSessions(sessions) {
    fs.writeFileSync(SESSION_FILE, JSON.stringify(sessions));
}

let sessions = loadSessions();
app.use(cors());
app.use(express.json());

// Helper function to extract response from Langflow
function extractResponse(data) {
    try {
        if (data.output) return data.output;
        if (data.outputs?.[0]?.outputs?.[0]?.results?.message?.text) {
            return data.outputs[0].outputs[0].results.message.text;
        }
        if (data.outputs?.[0]?.outputs?.[0]?.artifacts?.message) {
            return data.outputs[0].outputs[0].artifacts.message;
        }
        return JSON.stringify(data);
    } catch (e) {
        console.error('Error extracting response:', e);
        return "I received an unexpected response format";
    }
}

// API Endpoints
app.post('/api/chat', async (req, res) => {
    try {
        const sessionId = req.body.session_id || `session_${Date.now()}`;
        const isNewSession = !sessions[sessionId];

        if (isNewSession) {
            sessions[sessionId] = {
                name: req.body.session_name || `Chat ${new Date().toLocaleString()}`,
                messages: []
            };
        }

        if (req.body.input_value && req.body.input_value.trim() !== '') {
            const payload = {
                input_value: req.body.input_value,
                output_type: "chat",
                input_type: "chat",
                session_id: sessionId
            };

            const apiResponse = await fetch('https://lang.ibhost.online/api/v1/run/e3a03594-dea7-4747-9b99-c13f269c4510', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': process.env.LANGFLOW_API_KEY
                },
                body: JSON.stringify(payload)
            });

            const data = await apiResponse.json();
            const responseText = extractResponse(data);

            sessions[sessionId].messages.push({
                input: req.body.input_value,
                output: responseText,
                timestamp: new Date()
            });

            saveSessions(sessions);

            res.json({
                output: responseText,
                sessionId,
                sessionName: sessions[sessionId].name,
                isNewSession
            });
        } else {
            res.json({
                output: "Welcome! What would you like to discuss today?",
                sessionId,
                sessionName: sessions[sessionId].name,
                isNewSession
            });
        }
    } catch (error) {
        console.error('Chat error:', error);
        res.status(500).json({
            error: error.message,
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

app.get('/api/sessions', (req, res) => {
    res.json(Object.entries(sessions).map(([id, data]) => ({
        id,
        name: data.name,
        lastMessage: data.messages.length > 0
            ? data.messages[data.messages.length - 1].timestamp
            : null
    })).sort((a, b) => (b.lastMessage || 0) - (a.lastMessage || 0)));
});

app.get('/api/session/:id', (req, res) => {
    const session = sessions[req.params.id];
    res.json(session ? session.messages : []);
});

app.get('/api/init', (req, res) => {
    res.json({
        message: "Welcome! What would you like to discuss today?",
        sessions: Object.entries(sessions).map(([id, data]) => ({
            id,
            name: data.name,
            lastMessage: data.messages.length > 0
                ? data.messages[data.messages.length - 1].timestamp
                : null
        })).sort((a, b) => (b.lastMessage || 0) - (a.lastMessage || 0))
    });
});

// Start server
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
    console.log(`${Object.keys(sessions).length} sessions loaded`);
});