<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Math Mentor Chat</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3>Chat Sessions</h3>
                <button class="toggle-sidebar-btn" id="toggleSidebar">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            <div class="session-list-container">
                <div class="session-list" id="sessionList">
                    <!-- Sessions will be loaded here -->
                </div>
            </div>
            <button id="newSession" class="new-session-btn">
                <i class="fas fa-plus"></i> New Chat
            </button>
        </div>

        <!-- Resize Handle -->
        <div class="resize-handle" id="resizeHandle"></div>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <div class="chat-header">
                <button class="toggle-sidebar-btn mobile-only" id="toggleSidebarMobile">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Math Mentor</h1>
            </div>

            <div class="chat-messages" id="messages">
                <div class="ai-message message">
                    Welcome! Loading your chat sessions...
                </div>
            </div>

            <div class="chat-input">
                <div class="input-group">
                    <button id="dictateButton" title="Voice Input">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <input type="text" id="userInput" placeholder="Type your message..." disabled>
                    <button id="sendButton" disabled>Send</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>