document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const sidebar = document.getElementById('sidebar');
    const toggleSidebarBtn = document.getElementById('toggleSidebar');
    const toggleSidebarMobile = document.getElementById('toggleSidebarMobile');
    const messagesContainer = document.getElementById('messages');
    const userInput = document.getElementById('userInput');
    const sendButton = document.getElementById('sendButton');
    const dictateButton = document.getElementById('dictateButton');
    const sessionList = document.getElementById('sessionList');
    const newSessionBtn = document.getElementById('newSession');
    const testCodeButton = document.getElementById('testCodeButton');

    // State
    let currentSession = null;
    let isListening = false;
    let recognition = null;
    let isCollapsed = false;

    // Initialize
    loadSidebarState();
    initializeFirstSession();
    setupEventListeners();

    // Sidebar Functions
    function loadSidebarState() {
        const savedState = localStorage.getItem('sidebarCollapsed');
        if (savedState === 'true') {
            toggleSidebar();
        }
    }

    function toggleSidebar() {
        isCollapsed = !isCollapsed;
        sidebar.classList.toggle('collapsed');
        toggleSidebarBtn.innerHTML = isCollapsed
            ? '<i class="fas fa-chevron-right"></i>'
            : '<i class="fas fa-chevron-left"></i>';
        localStorage.setItem('sidebarCollapsed', isCollapsed);
    }

    // Session Management
    function initializeFirstSession() {
        fetch('http://localhost:3001/api/init')
            .then(res => res.json())
            .then(data => {
                messagesContainer.innerHTML = '';
                addMessage(data.message, false);

                if (data.sessions && data.sessions.length > 0) {
                    updateSessionList(data.sessions);
                }

                enableInput();
            });
    }

    function updateSessionList(sessions) {
        sessionList.innerHTML = '';
        sessions.forEach(session => {
            const item = document.createElement('div');
            item.className = 'session-item';
            item.textContent = session.name;
            item.dataset.id = session.id;
            item.addEventListener('click', () => loadSession(session.id));
            sessionList.appendChild(item);
        });
        highlightActiveSession();
    }

    function loadSession(sessionId) {
        messagesContainer.innerHTML = '';
        addMessage("Loading session...", false);
        disableInput();

        fetch(`http://localhost:3001/api/session/${sessionId}`)
            .then(res => res.json())
            .then(messages => {
                messagesContainer.innerHTML = '';
                if (messages.length === 0) {
                    addMessage("Welcome! Ask me anything about mathematics.", false);
                } else {
                    messages.forEach(msg => {
                        addMessage(msg.input, true);
                        addMessage(msg.output, false);
                    });
                }
                currentSession = sessionId;
                highlightActiveSession();
            })
            .catch(error => {
                addMessage(`Error loading session: ${error.message}`, false);
            })
            .finally(() => {
                enableInput();
                scrollToBottom();
            });
    }

    function createNewSession() {
        messagesContainer.innerHTML = '';
        currentSession = null;
        addMessage("Welcome! What would you like to discuss today?", false);
        enableInput();
        document.querySelectorAll('.session-item').forEach(item => {
            item.classList.remove('active');
        });
    }

    function highlightActiveSession() {
        document.querySelectorAll('.session-item').forEach(item => {
            item.classList.toggle('active', item.dataset.id === currentSession);
        });
    }

    // Message Functions
    function sendMessage() {
        const message = userInput.value.trim();
        if (!message) return;

        disableInput();
        addMessage(message, true);
        userInput.value = '';
        const loadingMessage = addMessage("", false, true);

        fetch('http://localhost:3001/api/chat', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                input_value: message,
                session_id: currentSession
            })
        })
        .then(res => res.json())
        .then(data => {
            loadingMessage.innerHTML = formatResponse(data.output);
            if (data.isNewSession) {
                currentSession = data.sessionId;
                fetch('http://localhost:3001/api/sessions')
                    .then(res => res.json())
                    .then(sessions => updateSessionList(sessions));
            }
        })
        .catch(error => {
            loadingMessage.innerHTML = `Error: ${error.message}`;
        })
        .finally(() => {
            enableInput();
            scrollToBottom();
        });
    }

    function addMessage(text, isUser, isLoading = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;

        if (isLoading) {
            messageDiv.innerHTML = `
                <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            `;
        } else {
            messageDiv.innerHTML = formatResponse(text);
        }

        messagesContainer.appendChild(messageDiv);
        scrollToBottom();
        return messageDiv;
    }

    function formatResponse(text) {
        if (!text) return '';

        // Escape HTML first
        text = escapeHtml(text);

        // First, try to detect and format unformatted code blocks
        text = detectAndFormatCode(text);

        // Convert markdown code blocks - handle both with and without newlines
        text = text.replace(/```(\w*)\n?([\s\S]*?)\n?```/g,
            (_, language, code) => {
                // Format the code properly
                const formattedCode = formatCodeBlock(code, language);
                return `<div class="code-container">
                    <div class="code-header">
                        <span>${language || 'code'}</span>
                        <button class="copy-btn" onclick="copyCodeToClipboard(this)">Copy</button>
                    </div>
                    <pre class="code-block">${formattedCode}</pre>
                </div>`;
            });

        // Handle inline code
        text = text.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

        // Convert other markdown
        text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // Handle line breaks and paragraphs for non-code content
        const hasCodeBlock = text.includes('<div class="code-container"');
        if (!hasCodeBlock) {
            text = text.replace(/\n\n/g, '</p><p>');
            text = text.replace(/\n/g, '<br>');
            text = `<p>${text}</p>`;
        } else {
            // For text with code blocks, we need to handle the parts outside code blocks
            const parts = text.split(/<div class="code-container">/);
            let result = parts[0].replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>');
            if (!result.includes('<p>')) result = `<p>${result}</p>`;

            for (let i = 1; i < parts.length; i++) {
                const [codeBlockPart, textPart] = parts[i].split('</div>', 2);
                result += '<div class="code-container">' + codeBlockPart + '</div>';

                if (textPart) {
                    let formattedText = textPart.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>');
                    if (!formattedText.includes('<p>')) formattedText = `<p>${formattedText}</p>`;
                    result += formattedText;
                }
            }

            text = result;
        }

        return text;
    }

    // Function to detect and format unformatted code
    function detectAndFormatCode(text) {
        // Look for long lines that appear to be code (contain common code patterns)
        const codePatterns = [
            /const\s+\w+\s*=/,
            /let\s+\w+\s*=/,
            /var\s+\w+\s*=/,
            /function\s+\w+\s*\(/,
            /document\./,
            /addEventListener/,
            /getElementById/,
            /querySelector/,
            /console\./,
            /if\s*\(/,
            /for\s*\(/,
            /while\s*\(/,
            /=>\s*\{/,
            /\.forEach\(/,
            /\.map\(/,
            /\.filter\(/,
            /\{\s*x:/,  // object literal pattern
            /canvas\./,
            /ctx\./
        ];

        // Check if text contains multiple code patterns and appears to be unformatted
        let codePatternCount = 0;
        codePatterns.forEach(pattern => {
            if (pattern.test(text)) codePatternCount++;
        });

        // More aggressive detection: if it looks like unformatted JavaScript code
        const hasMultipleStatements = text.includes(';') && text.split(';').length > 3;
        const isLongLine = text.length > 150;
        const hasLimitedNewlines = text.split('\n').length < 5;

        if ((codePatternCount >= 2 || hasMultipleStatements) && isLongLine && hasLimitedNewlines) {
            // Wrap it in a code block
            const formattedCode = formatJavaScript(text);
            return `\`\`\`javascript\n${formattedCode}\n\`\`\``;
        }

        return text;
    }

    // Function to format JavaScript code (using the working version from test script)
    function formatJavaScript(code) {
        let formatted = code
            .replace(/;(?!\s*[)}]|\s*$)/g, ';\n')
            .replace(/\{(?!\s*$)/g, '{\n')
            .replace(/(?<!^\s*)\}/g, '\n}')
            .replace(/,(?!\s*[}\]])/g, ',\n')
            .replace(/function\s+(\w+)\s*\([^)]*\)\s*\{/g, (match) => match + '\n')
            .replace(/(if|else|for|while|switch)\s*\([^)]*\)\s*\{/g, (match) => match + '\n')
            .replace(/\n\s*\n\s*\n/g, '\n\n')
            .split('\n')
            .map((line, index, lines) => {
                let indent = 0;
                for (let i = 0; i < index; i++) {
                    const openBraces = (lines[i].match(/\{/g) || []).length;
                    const closeBraces = (lines[i].match(/\}/g) || []).length;
                    indent += openBraces - closeBraces;
                }
                if (line.trim().startsWith('}')) {
                    indent = Math.max(0, indent - 1);
                }
                return '    '.repeat(Math.max(0, indent)) + line.trim();
            })
            .join('\n')
            .trim();

        return formatted;
    }

    // Function to format code blocks based on language
    function formatCodeBlock(code, language) {
        // Remove extra whitespace and normalize
        code = code.trim();

        // If it's JavaScript and appears to be unformatted, format it
        if ((language === 'javascript' || language === 'js' || !language) &&
            code.length > 100 && code.split('\n').length < 5) {
            return formatJavaScript(code);
        }

        return code;
    }

    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    }

    // Voice Dictation
    function startDictation() {
        if (!('webkitSpeechRecognition' in window)) {
            alert('Speech recognition not supported in your browser');
            return;
        }

        recognition = new webkitSpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;

        let finalTranscript = '';
        let timeout;

        recognition.onstart = () => {
            isListening = true;
            dictateButton.classList.add('listening');
            userInput.placeholder = "Listening...";
            userInput.value = '';
        };

        recognition.onresult = (event) => {
            clearTimeout(timeout);

            let interimTranscript = '';
            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                } else {
                    interimTranscript += transcript;
                }
            }

            userInput.value = finalTranscript + interimTranscript;

            timeout = setTimeout(() => {
                if (finalTranscript.trim()) {
                    sendMessage();
                }
                stopDictation();
            }, 2000);
        };

        recognition.onerror = (event) => {
            console.error('Speech recognition error', event.error);
            stopDictation();
        };

        recognition.onend = () => {
            stopDictation();
        };

        recognition.start();
    }

    function stopDictation() {
        if (recognition) {
            recognition.stop();
        }
        dictateButton.classList.remove('listening');
        isListening = false;
        userInput.placeholder = "Type your message...";
    }

    // UI Helpers
    function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    function enableInput() {
        userInput.disabled = false;
        sendButton.disabled = false;
        dictateButton.disabled = false;
        userInput.placeholder = "Type your message...";
        userInput.focus();
    }

    function disableInput() {
        userInput.disabled = true;
        sendButton.disabled = true;
        dictateButton.disabled = true;
        userInput.placeholder = "Processing...";
    }

    // Test function for code formatting
    function testCodeFormatting() {
        disableInput();
        addMessage("Testing code formatting...", true);
        const loadingMessage = addMessage("", false, true);

        fetch('http://localhost:3001/api/test-code', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        })
        .then(res => res.json())
        .then(data => {
            loadingMessage.innerHTML = formatResponse(data.output);
        })
        .catch(error => {
            loadingMessage.innerHTML = `Error: ${error.message}`;
        })
        .finally(() => {
            enableInput();
            scrollToBottom();
        });
    }

    // Event Listeners
    function setupEventListeners() {
        toggleSidebarBtn.addEventListener('click', toggleSidebar);
        toggleSidebarMobile.addEventListener('click', toggleSidebar);

        sendButton.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });

        dictateButton.addEventListener('click', () => {
            isListening ? stopDictation() : startDictation();
        });

        newSessionBtn.addEventListener('click', createNewSession);

        testCodeButton.addEventListener('click', testCodeFormatting);
    }

    // Expose functions globally for testing
    window.formatResponse = formatResponse;
    window.detectAndFormatCode = detectAndFormatCode;
    window.formatJavaScript = formatJavaScript;
    window.formatCodeBlock = formatCodeBlock;

    // Global function for copy button
    window.copyCodeToClipboard = async function(button) {
        const codeBlock = button.closest('.code-container').querySelector('.code-block');

        // Get the text content with preserved line breaks
        let textToCopy = codeBlock.textContent;

        const originalText = button.textContent;

        try {
            // Try modern Clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(textToCopy);
                button.textContent = 'Copied!';
            } else {
                // Fallback to older method
                const textarea = document.createElement('textarea');
                textarea.value = textToCopy;
                textarea.style.position = 'absolute';
                textarea.style.left = '-9999px';
                document.body.appendChild(textarea);
                textarea.select();

                const successful = document.execCommand('copy');
                document.body.removeChild(textarea);

                button.textContent = successful ? 'Copied!' : 'Failed';
            }

            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
        } catch (err) {
            console.error('Failed to copy: ', err);
            button.textContent = 'Failed';
            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
        }
    };
});
