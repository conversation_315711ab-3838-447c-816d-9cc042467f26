document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const sidebar = document.getElementById('sidebar');
    const toggleSidebarBtn = document.getElementById('toggleSidebar');
    const toggleSidebarMobile = document.getElementById('toggleSidebarMobile');
    const messagesContainer = document.getElementById('messages');
    const userInput = document.getElementById('userInput');
    const sendButton = document.getElementById('sendButton');
    const dictateButton = document.getElementById('dictateButton');
    const sessionList = document.getElementById('sessionList');
    const newSessionBtn = document.getElementById('newSession');

    // State
    let currentSession = null;
    let isListening = false;
    let recognition = null;
    let isCollapsed = false;

    // Initialize
    loadSidebarState();
    initializeFirstSession();
    setupEventListeners();

    // Sidebar Functions
    function loadSidebarState() {
        const savedState = localStorage.getItem('sidebarCollapsed');
        if (savedState === 'true') {
            toggleSidebar();
        }
    }

    function toggleSidebar() {
        isCollapsed = !isCollapsed;
        sidebar.classList.toggle('collapsed');
        toggleSidebarBtn.innerHTML = isCollapsed
            ? '<i class="fas fa-chevron-right"></i>'
            : '<i class="fas fa-chevron-left"></i>';
        localStorage.setItem('sidebarCollapsed', isCollapsed);
    }

    // Session Management
    function initializeFirstSession() {
        fetch('http://localhost:3001/api/init')
            .then(res => res.json())
            .then(data => {
                messagesContainer.innerHTML = '';
                addMessage(data.message, false);

                if (data.sessions && data.sessions.length > 0) {
                    updateSessionList(data.sessions);
                }

                enableInput();
            });
    }

    function updateSessionList(sessions) {
        sessionList.innerHTML = '';
        sessions.forEach(session => {
            const item = document.createElement('div');
            item.className = 'session-item';
            item.textContent = session.name;
            item.dataset.id = session.id;
            item.addEventListener('click', () => loadSession(session.id));
            sessionList.appendChild(item);
        });
        highlightActiveSession();
    }

    function loadSession(sessionId) {
        messagesContainer.innerHTML = '';
        addMessage("Loading session...", false);
        disableInput();

        fetch(`http://localhost:3001/api/session/${sessionId}`)
            .then(res => res.json())
            .then(messages => {
                messagesContainer.innerHTML = '';
                if (messages.length === 0) {
                    addMessage("Welcome! Ask me anything about mathematics.", false);
                } else {
                    messages.forEach(msg => {
                        addMessage(msg.input, true);
                        addMessage(msg.output, false);
                    });
                }
                currentSession = sessionId;
                highlightActiveSession();
            })
            .catch(error => {
                addMessage(`Error loading session: ${error.message}`, false);
            })
            .finally(() => {
                enableInput();
                scrollToBottom();
            });
    }

    function createNewSession() {
        messagesContainer.innerHTML = '';
        currentSession = null;
        addMessage("Welcome! What would you like to discuss today?", false);
        enableInput();
        document.querySelectorAll('.session-item').forEach(item => {
            item.classList.remove('active');
        });
    }

    function highlightActiveSession() {
        document.querySelectorAll('.session-item').forEach(item => {
            item.classList.toggle('active', item.dataset.id === currentSession);
        });
    }

    // Message Functions
    function sendMessage() {
        const message = userInput.value.trim();
        if (!message) return;

        disableInput();
        addMessage(message, true);
        userInput.value = '';
        const loadingMessage = addMessage("", false, true);

        fetch('http://localhost:3001/api/chat', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                input_value: message,
                session_id: currentSession
            })
        })
        .then(res => res.json())
        .then(data => {
            loadingMessage.innerHTML = formatResponse(data.output);
            if (data.isNewSession) {
                currentSession = data.sessionId;
                fetch('http://localhost:3001/api/sessions')
                    .then(res => res.json())
                    .then(sessions => updateSessionList(sessions));
            }
        })
        .catch(error => {
            loadingMessage.innerHTML = `Error: ${error.message}`;
        })
        .finally(() => {
            enableInput();
            scrollToBottom();
        });
    }

    function addMessage(text, isUser, isLoading = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;

        if (isLoading) {
            messageDiv.innerHTML = `
                <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            `;
        } else {
            messageDiv.innerHTML = formatResponse(text);
        }

        messagesContainer.appendChild(messageDiv);
        scrollToBottom();
        return messageDiv;
    }

    function formatResponse(text) {
        if (!text) return '';

        // Simple approach: detect unformatted code and wrap it, then handle everything else normally

        // Universal code detection - works for ANY programming language
        const codeIndicators = [
            // Common programming patterns
            /\w+\s*=\s*\w+/,           // variable assignment
            /\w+\(\s*.*\s*\)/,         // function calls
            /\{[\s\S]*\}/,             // code blocks with braces
            /\w+\.\w+/,                // object/method access
            /import\s+\w+/,            // imports
            /from\s+\w+/,              // from imports
            /def\s+\w+/,               // function definitions
            /class\s+\w+/,             // class definitions
            /function\s+\w+/,          // function keyword
            /const\s+\w+/,             // const declarations
            /let\s+\w+/,               // let declarations
            /var\s+\w+/,               // var declarations
            /if\s*\(/,                 // if statements
            /for\s*\(/,                // for loops
            /while\s*\(/,              // while loops
            /\w+::\w+/,                // C++ scope resolution
            /\w+\[\w*\]/,              // array/list access
            /@\w+/,                    // decorators/annotations
            /\$\w+/,                   // PHP variables
            /\w+\s*->\s*\w+/,          // arrow operators
            /\w+\s*=>\s*\w+/,          // arrow functions
            /\w+\s*::\s*\w+/,          // static access
        ];

        let codeIndicatorCount = 0;
        codeIndicators.forEach(pattern => {
            if (pattern.test(text)) codeIndicatorCount++;
        });

        const isLongLine = text.length > 150;
        const hasLimitedNewlines = text.split('\n').length < 4;
        const hasMultipleStatements = text.includes(';') && text.split(';').length > 3;

        // If it looks like unformatted code (ANY language), format it
        if ((codeIndicatorCount >= 3 || hasMultipleStatements) && isLongLine && hasLimitedNewlines) {
            const formattedCode = formatUniversalCode(text);
            text = `\`\`\`\n${formattedCode}\n\`\`\``;
        }

        // Now process normally
        // Escape HTML
        text = escapeHtml(text);

        // Handle code blocks
        text = text.replace(/```(\w*)\n?([\s\S]*?)\n?```/g, (_, language, code) => {
            return `<div class="code-container">
                <div class="code-header">
                    <span>${language || 'code'}</span>
                    <button class="copy-btn" onclick="copyCodeToClipboard(this)">Copy</button>
                </div>
                <pre class="code-block">${code.trim()}</pre>
            </div>`;
        });

        // Handle inline code
        text = text.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

        // Handle markdown formatting
        text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // Handle line breaks - simple approach
        text = text.replace(/\n\n/g, '</p><p>');
        text = text.replace(/\n/g, '<br>');

        // Wrap in paragraphs if no code blocks
        if (!text.includes('<div class="code-container">')) {
            text = `<p>${text}</p>`;
        } else {
            // Handle mixed content more carefully
            const parts = text.split(/(<div class="code-container">[\s\S]*?<\/div>)/);
            let result = '';

            for (let i = 0; i < parts.length; i++) {
                if (parts[i].includes('code-container')) {
                    result += parts[i];
                } else if (parts[i].trim()) {
                    result += `<p>${parts[i]}</p>`;
                }
            }
            text = result;
        }

        return text;
    }

    // Simple JavaScript formatter
    function formatJavaScript(code) {
        return code
            .replace(/;(?!\s*[)}]|\s*$)/g, ';\n')
            .replace(/\{(?!\s*$)/g, '{\n')
            .replace(/(?<!^\s*)\}/g, '\n}')
            .replace(/,(?!\s*[}\]])/g, ',\n')
            .replace(/function\s+(\w+)\s*\([^)]*\)\s*\{/g, (match) => match + '\n')
            .replace(/(if|else|for|while|switch)\s*\([^)]*\)\s*\{/g, (match) => match + '\n')
            .replace(/\n\s*\n\s*\n/g, '\n\n')
            .split('\n')
            .map((line, index, lines) => {
                let indent = 0;
                for (let i = 0; i < index; i++) {
                    const openBraces = (lines[i].match(/\{/g) || []).length;
                    const closeBraces = (lines[i].match(/\}/g) || []).length;
                    indent += openBraces - closeBraces;
                }
                if (line.trim().startsWith('}')) {
                    indent = Math.max(0, indent - 1);
                }
                return '    '.repeat(Math.max(0, indent)) + line.trim();
            })
            .join('\n')
            .trim();
    }

    // Python formatter
    function formatPython(code) {
        // Split by common Python keywords that should be on new lines
        let formatted = code
            // Add newlines before import statements
            .replace(/(\w)import\s+/g, '$1\nimport ')
            .replace(/(\w)from\s+/g, '$1\nfrom ')
            // Add newlines before function and class definitions
            .replace(/(\w)def\s+/g, '$1\ndef ')
            .replace(/(\w)class\s+/g, '$1\nclass ')
            // Add newlines before if/for/while statements
            .replace(/(\w)if\s+/g, '$1\nif ')
            .replace(/(\w)for\s+/g, '$1\nfor ')
            .replace(/(\w)while\s+/g, '$1\nwhile ')
            .replace(/(\w)elif\s+/g, '$1\nelif ')
            .replace(/(\w)else:/g, '$1\nelse:')
            .replace(/(\w)try:/g, '$1\ntry:')
            .replace(/(\w)except/g, '$1\nexcept')
            .replace(/(\w)finally:/g, '$1\nfinally:')
            // Clean up multiple newlines
            .replace(/\n\s*\n\s*\n/g, '\n\n')
            .trim();

        // Split into lines and add proper indentation
        const lines = formatted.split('\n');
        let indentLevel = 0;
        const indentedLines = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) {
                indentedLines.push('');
                continue;
            }

            // Decrease indent for certain keywords
            if (line.startsWith('def ') || line.startsWith('class ') ||
                line.startsWith('if ') || line.startsWith('elif ') ||
                line.startsWith('else:') || line.startsWith('for ') ||
                line.startsWith('while ') || line.startsWith('try:') ||
                line.startsWith('except') || line.startsWith('finally:')) {

                // Reset indent for top-level constructs
                if (line.startsWith('def ') || line.startsWith('class ') ||
                    line.startsWith('if __name__')) {
                    indentLevel = 0;
                } else if (indentLevel > 0) {
                    // Keep current indent for nested constructs
                }
            }

            // Add the line with current indentation
            indentedLines.push('    '.repeat(indentLevel) + line);

            // Increase indent after colons (function/class/if/for/while definitions)
            if (line.endsWith(':')) {
                indentLevel++;
            }
        }

        return indentedLines.join('\n');
    }

    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    }

    // Voice Dictation
    function startDictation() {
        if (!('webkitSpeechRecognition' in window)) {
            alert('Speech recognition not supported in your browser');
            return;
        }

        recognition = new webkitSpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;

        let finalTranscript = '';
        let timeout;

        recognition.onstart = () => {
            isListening = true;
            dictateButton.classList.add('listening');
            userInput.placeholder = "Listening...";
            userInput.value = '';
        };

        recognition.onresult = (event) => {
            clearTimeout(timeout);

            let interimTranscript = '';
            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                } else {
                    interimTranscript += transcript;
                }
            }

            userInput.value = finalTranscript + interimTranscript;

            timeout = setTimeout(() => {
                if (finalTranscript.trim()) {
                    sendMessage();
                }
                stopDictation();
            }, 2000);
        };

        recognition.onerror = (event) => {
            console.error('Speech recognition error', event.error);
            stopDictation();
        };

        recognition.onend = () => {
            stopDictation();
        };

        recognition.start();
    }

    function stopDictation() {
        if (recognition) {
            recognition.stop();
        }
        dictateButton.classList.remove('listening');
        isListening = false;
        userInput.placeholder = "Type your message...";
    }

    // UI Helpers
    function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    function enableInput() {
        userInput.disabled = false;
        sendButton.disabled = false;
        dictateButton.disabled = false;
        userInput.placeholder = "Type your message...";
        userInput.focus();
    }

    function disableInput() {
        userInput.disabled = true;
        sendButton.disabled = true;
        dictateButton.disabled = true;
        userInput.placeholder = "Processing...";
    }



    // Event Listeners
    function setupEventListeners() {
        toggleSidebarBtn.addEventListener('click', toggleSidebar);
        toggleSidebarMobile.addEventListener('click', toggleSidebar);

        sendButton.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });

        dictateButton.addEventListener('click', () => {
            isListening ? stopDictation() : startDictation();
        });

        newSessionBtn.addEventListener('click', createNewSession);
    }



    // Global function for copy button
    window.copyCodeToClipboard = async function(button) {
        const codeBlock = button.closest('.code-container').querySelector('.code-block');

        // Get the text content with preserved line breaks
        let textToCopy = codeBlock.textContent;

        const originalText = button.textContent;

        try {
            // Try modern Clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(textToCopy);
                button.textContent = 'Copied!';
            } else {
                // Fallback to older method
                const textarea = document.createElement('textarea');
                textarea.value = textToCopy;
                textarea.style.position = 'absolute';
                textarea.style.left = '-9999px';
                document.body.appendChild(textarea);
                textarea.select();

                const successful = document.execCommand('copy');
                document.body.removeChild(textarea);

                button.textContent = successful ? 'Copied!' : 'Failed';
            }

            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
        } catch (err) {
            console.error('Failed to copy: ', err);
            button.textContent = 'Failed';
            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
        }
    };
});
