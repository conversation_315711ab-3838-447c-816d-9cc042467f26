/* Layout Variables */
:root {
  --sidebar-bg: #2c3e50;
  --sidebar-text: #ecf0f1;
  --main-bg: #f5f5f5;
  --chat-header-bg: #4a6fa5;
  --chat-header-text: white;
  --user-message-bg: #4a6fa5;
  --ai-message-bg: #e5e5ea;
  --button-bg: #4a6fa5;
  --button-hover: #3a5a8f;
  --error-color: #e74c3c;
  --code-bg: #282c34;
  --code-text: #abb2bf;
  --transition-speed: 0.3s;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  height: 100vh;
  overflow: hidden;
  background-color: var(--main-bg);
}

/* App Container */
.app-container {
  display: flex;
  height: 100%;
  width: 100%;
}

/* Sidebar Styles */
.sidebar {
  width: 25%;
  min-width: 250px;
  max-width: 350px;
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  z-index: 100;
  overflow: hidden;
  transition: all var(--transition-speed) ease;
}

.sidebar.collapsed {
  width: 60px;
  min-width: 60px;
}

.sidebar-header {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 60px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.2rem;
  white-space: nowrap;
  transition: opacity var(--transition-speed) ease;
}

.sidebar.collapsed .sidebar-header h3 {
  opacity: 0;
  pointer-events: none;
}

.toggle-sidebar-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--button-bg);
  border: none;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 110;
  transition: all var(--transition-speed) ease;
}

.toggle-sidebar-btn:hover {
  background-color: var(--button-hover);
}

.session-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  transition: opacity var(--transition-speed) ease;
}

.sidebar.collapsed .session-list-container {
  opacity: 0;
  pointer-events: none;
}

.session-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.session-item {
  padding: 10px 12px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.session-item.active {
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: bold;
}

.new-session-btn {
  padding: 12px;
  margin: 10px;
  background-color: var(--button-bg);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: opacity var(--transition-speed) ease;
}

.sidebar.collapsed .new-session-btn {
  opacity: 0;
  pointer-events: none;
}

.new-session-btn:hover {
  background-color: var(--button-hover);
}

/* Main Content Styles */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  transition: margin-left var(--transition-speed) ease;
}

.sidebar.collapsed + .main-content {
  margin-left: 60px;
}

.chat-header {
  background-color: var(--chat-header-bg);
  color: var(--chat-header-text);
  padding: 15px;
  display: flex;
  align-items: center;
  min-height: 60px;
}

.chat-header h1 {
  margin: 0;
  font-size: 1.5rem;
  flex: 1;
  text-align: center;
}

.toggle-sidebar-btn.mobile-only {
  display: none;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: white;
  display: flex;
  flex-direction: column;
}

.chat-input {
  padding: 15px;
  background-color: var(--main-bg);
  border-top: 1px solid #ddd;
}

.input-group {
  display: flex;
  gap: 10px;
}

#userInput {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
}

#sendButton, #dictateButton {
  padding: 0 20px;
  background-color: var(--button-bg);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

#dictateButton.listening {
  background-color: var(--error-color);
  animation: pulse 1.5s infinite;
}

/* Message Styles */
.message {
  max-width: 85%;
  padding: 14px 18px;
  margin-bottom: 20px;
  border-radius: 12px;
  line-height: 1.5;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.user-message {
  background-color: var(--user-message-bg);
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 4px;
}

.ai-message {
  background-color: var(--ai-message-bg);
  margin-right: auto;
  border-bottom-left-radius: 4px;
}

/* Message formatting */
.message p {
  margin-bottom: 1em;
  line-height: 1.6;
}

.message p:last-child {
  margin-bottom: 0;
}

.message ul, .message ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.message li {
  margin-bottom: 0.3em;
}

/* Code Block Styling */
.code-container {
  position: relative;
  margin: 1em 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.code-header {
  background: #1e1e1e;
  color: #9cdcfe;
  padding: 6px 12px;
  font-family: 'Consolas', monospace;
  font-size: 0.85em;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.code-block {
  background: var(--code-bg);
  color: var(--code-text);
  padding: 1em;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
  line-height: 1.5;
  white-space: pre !important;
  overflow-x: auto;
  margin: 0;
  tab-size: 2;
}

pre {
  white-space: pre !important;
}

.inline-code {
  background: rgba(175, 184, 193, 0.2);
  color: #eb5757;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Consolas', monospace;
  font-size: 0.9em;
}

/* Headers inside messages */
.message h1, 
.message h2, 
.message h3, 
.message h4, 
.message h5, 
.message h6 {
  margin: 1em 0 0.5em 0;
  font-weight: bold;
  line-height: 1.2;
}

.message h1 { font-size: 1.5em; }
.message h2 { font-size: 1.3em; }
.message h3 { font-size: 1.1em; }

/* Typing indicator */
.typing-indicator {
  display: flex;
  padding: 10px;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background-color: #4a6fa5;
  border-radius: 50%;
  display: inline-block;
  animation: bounce 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

/* Animations */
@keyframes bounce {
  0%, 80%, 100% { 
    transform: scale(0);
    opacity: 0.5;
  }
  40% { 
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.sidebar ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    width: 280px;
    height: 100vh;
    transform: translateX(-100%);
    z-index: 100;
  }

  .sidebar.visible {
    transform: translateX(0);
  }

  .main-content {
    width: 100%;
    margin-left: 0 !important;
  }

  .toggle-sidebar-btn:not(.mobile-only) {
    display: none;
  }
  
  .toggle-sidebar-btn.mobile-only {
    display: block;
    position: static;
    margin-right: 10px;
  }
}
