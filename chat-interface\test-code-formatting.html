<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Formatting Test</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .unformatted-code {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Code Formatting Test</h1>
    
    <div class="test-section">
        <h3>Before: Unformatted Code (as AI typically sends it)</h3>
        <div class="unformatted-code">
            const canvas = document.getElementById('gameCanvas');const ctx = canvas.getContext('2d');const scoreElement = document.getElementById('score');const gridSize = 20;const tileCount = 20;let score = 0;let snake = [    {x: 10, y: 10}];let food = {    x: Math.floor(Math.random() * tileCount),    y: Math.floor(Math.random() * tileCount)};let xVelocity = 0;let yVelocity = 0;let gameSpeed = 100;let gameRunning = true;canvas.width = gridSize * tileCount;canvas.height = gridSize * tileCount;document.addEventListener('keydown', changeDirection);function gameLoop() {    if (!gameRunning) return;        setTimeout(() => {        if (gameRunning) {            clearCanvas();            moveSnake();            drawSnake();            drawFood();            checkCollision();            gameLoop();        }    }, gameSpeed);}
        </div>
    </div>

    <div class="test-section">
        <h3>After: Properly Formatted Code (with our fix)</h3>
        <div id="formatted-output"></div>
    </div>

    <script>
        // Import the formatting functions from script.js
        const unformattedCode = `const canvas = document.getElementById('gameCanvas');const ctx = canvas.getContext('2d');const scoreElement = document.getElementById('score');const gridSize = 20;const tileCount = 20;let score = 0;let snake = [    {x: 10, y: 10}];let food = {    x: Math.floor(Math.random() * tileCount),    y: Math.floor(Math.random() * tileCount)};let xVelocity = 0;let yVelocity = 0;let gameSpeed = 100;let gameRunning = true;canvas.width = gridSize * tileCount;canvas.height = gridSize * tileCount;document.addEventListener('keydown', changeDirection);function gameLoop() {    if (!gameRunning) return;        setTimeout(() => {        if (gameRunning) {            clearCanvas();            moveSnake();            drawSnake();            drawFood();            checkCollision();            gameLoop();        }    }, gameSpeed);}function clearCanvas() {    ctx.fillStyle = 'white';    ctx.fillRect(0, 0, canvas.width, canvas.height);}function drawSnake() {    ctx.fillStyle = 'green';    snake.forEach(segment => {        ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize, gridSize);    });}`;

        // Function to format JavaScript code (copied from script.js)
        function formatJavaScript(code) {
            let formatted = code
                .replace(/;(?!\s*[)}]|\s*$)/g, ';\n')
                .replace(/\{(?!\s*$)/g, '{\n')
                .replace(/(?<!^\s*)\}/g, '\n}')
                .replace(/,(?!\s*[}\]])/g, ',\n')
                .replace(/function\s+(\w+)\s*\([^)]*\)\s*\{/g, (match) => match + '\n')
                .replace(/(if|else|for|while|switch)\s*\([^)]*\)\s*\{/g, (match) => match + '\n')
                .replace(/\n\s*\n\s*\n/g, '\n\n')
                .split('\n')
                .map((line, index, lines) => {
                    let indent = 0;
                    for (let i = 0; i < index; i++) {
                        const openBraces = (lines[i].match(/\{/g) || []).length;
                        const closeBraces = (lines[i].match(/\}/g) || []).length;
                        indent += openBraces - closeBraces;
                    }
                    if (line.trim().startsWith('}')) {
                        indent = Math.max(0, indent - 1);
                    }
                    return '    '.repeat(Math.max(0, indent)) + line.trim();
                })
                .join('\n')
                .trim();
            
            return formatted;
        }

        // Format the code and display it
        const formattedCode = formatJavaScript(unformattedCode);
        const codeContainer = `
            <div class="code-container">
                <div class="code-header">
                    <span>javascript</span>
                    <button class="copy-btn" onclick="copyFormattedCode()">Copy</button>
                </div>
                <pre class="code-block">${formattedCode}</pre>
            </div>
        `;
        
        document.getElementById('formatted-output').innerHTML = codeContainer;

        // Copy function for the test
        function copyFormattedCode() {
            const codeBlock = document.querySelector('.code-block');
            const textToCopy = codeBlock.textContent;
            
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(textToCopy).then(() => {
                    alert('Code copied to clipboard!');
                });
            } else {
                const textarea = document.createElement('textarea');
                textarea.value = textToCopy;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                alert('Code copied to clipboard!');
            }
        }
    </script>
</body>
</html>
