<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Formatting Test</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-input {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: monospace;
        }
        .test-button {
            background: #4a6fa5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #3a5a8f;
        }
        .output {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Live Code Formatting Test</h1>
        
        <div class="test-section">
            <h3>Test the Actual Formatting Function</h3>
            <p>Paste unformatted code here and test the formatting:</p>
            
            <textarea class="test-input" id="testInput" placeholder="Paste your unformatted code here...">const canvas = document.getElementById('gameCanvas');const ctx = canvas.getContext('2d');const scoreElement = document.getElementById('score');const gridSize = 20;const tileCount = 20;let score = 0;let snake = [    {x: 10, y: 10}];let food = {    x: Math.floor(Math.random() * tileCount),    y: Math.floor(Math.random() * tileCount)};let xVelocity = 0;let yVelocity = 0;let gameSpeed = 100;let gameRunning = true;canvas.width = gridSize * tileCount;canvas.height = gridSize * tileCount;document.addEventListener('keydown', changeDirection);function gameLoop() {    if (!gameRunning) return;        setTimeout(() => {        if (gameRunning) {            clearCanvas();            moveSnake();            drawSnake();            drawFood();            checkCollision();            gameLoop();        }    }, gameSpeed);}</textarea>
            
            <button class="test-button" onclick="testFormatting()">Test Formatting</button>
            <button class="test-button" onclick="clearOutput()">Clear</button>
            
            <div class="output" id="output"></div>
        </div>

        <div class="test-section">
            <h3>Simulate AI Message</h3>
            <p>This simulates how the AI message would be processed:</p>
            <button class="test-button" onclick="simulateAIMessage()">Simulate AI Response</button>
            <div class="output" id="aiOutput"></div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        function testFormatting() {
            const input = document.getElementById('testInput').value;
            const output = document.getElementById('output');
            
            // Use the same functions from script.js
            console.log('Testing with input:', input.substring(0, 100) + '...');
            
            // Simulate the formatResponse function
            let text = input;
            
            // Escape HTML first (simulate escapeHtml function)
            text = text.replace(/[&<>"']/g, function(m) {
                const map = {'&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#039;'};
                return map[m];
            });
            
            // Try to detect and format unformatted code blocks
            text = detectAndFormatCode(text);
            
            // Convert markdown code blocks
            text = text.replace(/```(\w*)\n?([\s\S]*?)\n?```/g, 
                (_, language, code) => {
                    const formattedCode = formatCodeBlock(code, language);
                    return `<div class="code-container">
                        <div class="code-header">
                            <span>${language || 'code'}</span>
                            <button class="copy-btn" onclick="copyCodeToClipboard(this)">Copy</button>
                        </div>
                        <pre class="code-block">${formattedCode}</pre>
                    </div>`;
                });
            
            output.innerHTML = text;
        }

        function simulateAIMessage() {
            const aiOutput = document.getElementById('aiOutput');
            const unformattedCode = document.getElementById('testInput').value;
            
            // Create a message div like the actual chat
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ai-message';
            
            // Use the actual formatResponse function from script.js
            if (typeof window.formatResponse === 'function') {
                messageDiv.innerHTML = window.formatResponse(unformattedCode);
            } else {
                // Fallback if function is not available
                messageDiv.innerHTML = 'formatResponse function not available. Check console for errors.';
            }
            
            aiOutput.innerHTML = '';
            aiOutput.appendChild(messageDiv);
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '';
            document.getElementById('aiOutput').innerHTML = '';
        }

        // Make formatResponse available globally for testing
        if (typeof window.formatResponse === 'undefined') {
            // If the function is not available from script.js, we'll need to access it differently
            console.log('formatResponse not available globally. Functions are likely in a closure.');
        }
    </script>
</body>
</html>
