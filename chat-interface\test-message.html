<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Message Formatting</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
        }
        .test-button {
            background: #4a6fa5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #3a5a8f;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test AI Message Formatting</h1>
        
        <button class="test-button" onclick="testUnformattedCode()">Test Unformatted Code</button>
        <button class="test-button" onclick="testFormattedCode()">Test Already Formatted Code</button>
        <button class="test-button" onclick="testMixedContent()">Test Mixed Content</button>
        <button class="test-button" onclick="clearMessages()">Clear</button>
        
        <div id="messages" class="chat-messages" style="margin-top: 20px; min-height: 200px;">
            <!-- Messages will appear here -->
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        function addTestMessage(content) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ai-message';
            messageDiv.innerHTML = window.formatResponse(content);
            messagesContainer.appendChild(messageDiv);
        }

        function testUnformattedCode() {
            const unformattedCode = `const canvas = document.getElementById('gameCanvas');const ctx = canvas.getContext('2d');const scoreElement = document.getElementById('score');const gridSize = 20;const tileCount = 20;let score = 0;let snake = [    {x: 10, y: 10}];let food = {    x: Math.floor(Math.random() * tileCount),    y: Math.floor(Math.random() * tileCount)};let xVelocity = 0;let yVelocity = 0;let gameSpeed = 100;let gameRunning = true;canvas.width = gridSize * tileCount;canvas.height = gridSize * tileCount;document.addEventListener('keydown', changeDirection);function gameLoop() {    if (!gameRunning) return;        setTimeout(() => {        if (gameRunning) {            clearCanvas();            moveSnake();            drawSnake();            drawFood();            checkCollision();            gameLoop();        }    }, gameSpeed);}function clearCanvas() {    ctx.fillStyle = 'white';    ctx.fillRect(0, 0, canvas.width, canvas.height);}`;
            
            addTestMessage("Here's a Snake game implementation:\n\n" + unformattedCode);
        }

        function testFormattedCode() {
            const formattedCode = `\`\`\`javascript
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const scoreElement = document.getElementById('score');

function gameLoop() {
    if (!gameRunning) return;
    
    setTimeout(() => {
        if (gameRunning) {
            clearCanvas();
            moveSnake();
            drawSnake();
            drawFood();
            checkCollision();
            gameLoop();
        }
    }, gameSpeed);
}
\`\`\``;
            
            addTestMessage("Here's a properly formatted Snake game:\n\n" + formattedCode);
        }

        function testMixedContent() {
            const mixedContent = `Here's how to create a simple game:

First, set up the canvas: const canvas = document.getElementById('gameCanvas');const ctx = canvas.getContext('2d');const gridSize = 20;

Then create the game loop:

\`\`\`javascript
function gameLoop() {
    if (!gameRunning) return;
    
    setTimeout(() => {
        clearCanvas();
        moveSnake();
        drawSnake();
        drawFood();
        checkCollision();
        gameLoop();
    }, gameSpeed);
}
\`\`\`

And finally add event listeners: document.addEventListener('keydown', changeDirection);`;
            
            addTestMessage(mixedContent);
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }
    </script>
</body>
</html>
